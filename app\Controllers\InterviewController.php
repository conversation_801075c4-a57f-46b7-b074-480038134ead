<?php

namespace App\Controllers;

use App\Models\PositionsGroupModel;
use App\Models\PositionModel;
use App\Models\ApplicantModel;
use App\Models\DakoiiOrgModel;
// Include FPDF library
require_once APPPATH . 'ThirdParty/fpdf/fpdf.php';

class InterviewController extends BaseController
{
    protected $positionsGroupModel;
    protected $positionModel;
    protected $applicantModel;
    protected $dakoiiOrgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionModel();
        $this->applicantModel = new ApplicantModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }

    /**
     * Send interview notification email to a single applicant
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function sendNotifications()
    {
        // Log the request method for debugging
        log_message('debug', 'Request method: ' . $this->request->getMethod());
        log_message('debug', 'Request path: ' . $this->request->getPath());

        // Log POST data for debugging
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        // Redirect to the process method
        return $this->processNotifications();
    }

    /**
     * Process the notification form submission
     *
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    private function processNotifications()
    {

        $orgId = session('org_id');
        $org = $this->dakoiiOrgModel->find($orgId);

        // Get interview settings
        $interviewSettings = json_decode($org['interview_settings'] ?? '{}', true);

        // Get applicant ID from the form
        $applicantIds = $this->request->getPost('applicant_ids');
        $recipientEmail = $this->request->getPost('recipient_email');

        if (empty($applicantIds) || empty($recipientEmail)) {
            return redirect()->to('reports/interviewSchedule')->with('error', 'Missing required information');
        }

        // Get the first applicant ID (we're only processing one)
        $applicantId = $applicantIds[0];

        // Skip test notifications
        if ($applicantId == 0) {
            return redirect()->to('reports/interviewSchedule')->with('success', 'Test notification sent successfully');
        }

        // Get applicant details
        $applicant = $this->applicantModel->find($applicantId);
        if (!$applicant) {
            return redirect()->to('reports/interviewSchedule')->with('error', 'Applicant not found');
        }

        // Get position details
        $position = $this->positionModel->find($applicant['position_id']);
        if (!$position) {
            return redirect()->to('reports/interviewSchedule')->with('error', 'Position not found');
        }

        // Get interview date and time from form submission
        $applicant['date'] = $this->request->getPost('interview_date') ?? date('Y-m-d');
        $applicant['start_time'] = $this->request->getPost('start_time') ?? '09:00:00';
        $applicant['end_time'] = $this->request->getPost('end_time') ?? '09:30:00';

        // Create notification data to store in the database
        $notificationData = [
            'uid' => uniqid('notice_'),
            'sender_email' => '<EMAIL>',
            'receiver_email' => $recipientEmail,
            'sent_datetime' => date('Y-m-d H:i:s'),
        ];

        // Generate PDF notification
        try {
            $pdfPath = $this->generateNotificationPDF($applicant, $position, $org, $interviewSettings, $notificationData);

            if (!$pdfPath) {
                log_message('error', 'PDF generation failed: No path returned');
                return redirect()->to('reports/interviewSchedule')->with('error', 'Failed to generate PDF notification. Check error logs for details.');
            }
        } catch (\Exception $e) {
            log_message('error', 'PDF generation exception: ' . $e->getMessage());
            return redirect()->to('reports/interviewSchedule')->with('error', 'Error generating PDF: ' . $e->getMessage());
        }

        // Send email with PDF attachment
        try {
            $emailResult = $this->sendNotificationEmail($applicant, $position, $org, $interviewSettings, $pdfPath, $recipientEmail);

            // Delete the temporary PDF file
            if (file_exists($pdfPath)) {
                unlink($pdfPath);
            }
        } catch (\Exception $e) {
            // Delete the temporary PDF file even if email sending fails
            if (file_exists($pdfPath)) {
                unlink($pdfPath);
            }

            log_message('error', 'Email sending exception: ' . $e->getMessage());
            return redirect()->to('reports/interviewSchedule')->with('error', 'Error sending email: ' . $e->getMessage());
        }

        if ($emailResult['status']) {
            // Store notification data in the database
            $existingNotices = json_decode($applicant['interview_notices'] ?? '[]', true);
            $existingNotices[] = $notificationData;

            $this->applicantModel->update($applicantId, [
                'interview_notices' => json_encode($existingNotices)
            ]);

            return redirect()->to('reports/interviewSchedule')->with('success', "Successfully sent interview notification to {$recipientEmail}");
        } else {
            return redirect()->to('reports/interviewSchedule')->with('error', "Failed to send notification: {$emailResult['message']}");
        }
    }

    /**
     * Generate PDF notification letter for an applicant
     */
    private function generateNotificationPDF($applicant, $position, $org, $interviewSettings, $notificationData = null)
    {
        // Check if the TCPDF file exists
        $tcpdfPath = ROOTPATH . 'vendor/tecnickcom/tcpdf/tcpdf.php';
        if (!file_exists($tcpdfPath)) {
            log_message('error', 'TCPDF library not found at: ' . $tcpdfPath);
            return false;
        }

        try {
            // Include TCPDF library
            require_once $tcpdfPath;

            // Create new PDF document - use TCPDF instead of FPDF
            $pdf = new \TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('SelMasta');
        $pdf->SetAuthor($org['name']);
        $pdf->SetTitle('Interview Notification');
        $pdf->SetSubject('Interview Notification for ' . $position['designation']);

        // Remove default header/footer
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Set margins
        $pdf->SetMargins(20, 20, 20);

        // Set auto page breaks
        $pdf->SetAutoPageBreak(true, 20);

        // Add a page
        $pdf->AddPage();

        // Get applicant's first name
        $nameParts = explode(' ', $applicant['name'] ?? 'Applicant');
        $firstName = $nameParts[0];

        // Get today's date
        $today = date('jS F Y');

        // Get interview date and time with error checking
        $interviewDate = isset($applicant['date']) ? date('l, j F Y', strtotime($applicant['date'])) : 'To be confirmed';

        $startTime = isset($applicant['start_time']) ? date('h:i A', strtotime($applicant['start_time'])) : '09:00 AM';
        $endTime = isset($applicant['end_time']) ? date('h:i A', strtotime($applicant['end_time'])) : '09:30 AM';
        $interviewTime = $startTime . ' - ' . $endTime;

        // Organization header with logo
        if (!empty($org['org_logo'])) {
            $logoPath = FCPATH . $org['org_logo'];
            if (file_exists($logoPath)) {
                $pdf->Image($logoPath, 20, 15, 20); // Smaller logo
                $pdf->SetY(40); // Set position after logo
            }
        }

        // SelMasta System name
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 6, 'SelMasta Recuitment and Selection System', 0, 1, 'C');

        // Organization name - professional spacing
        $pdf->SetFont('helvetica', 'B', 16);
        $pdf->Cell(0, 8, strtoupper($org['name']), 0, 1, 'C');

        // Add "Notification Letter" below organization name
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 6, 'INTERVIEW NOTIFICATION LETTER', 0, 1, 'C');
        $pdf->Ln(8); // Professional spacing

        // Date - right aligned
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 5, 'Date: ' . $today, 0, 1, 'R');
        $pdf->Ln(6);

        // Applicant details
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->Cell(0, 6, $applicant['name'] ?? 'Applicant', 0, 1);

        $pdf->SetFont('helvetica', '', 10);
        $contactDetails = isset($applicant['contact_details']) ? $applicant['contact_details'] :
                         (isset($applicant['email']) ? 'Email: ' . $applicant['email'] : 'No contact details available');
        $pdf->MultiCell(0, 5, $contactDetails, 0, 'L');
        $pdf->Ln(4);

        // Salutation
        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(0, 6, 'Dear ' . $firstName . ',', 0, 1);
        $pdf->Ln(2);

        // Subject line
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->MultiCell(0, 6, 'RE: Interview Notification for ' . $position['designation'] . ' (' . $position['position_no'] . ')', 0, 'L');
        $pdf->Ln(3);

        // Body text - professional spacing
        $pdf->SetFont('helvetica', '', 10);
        $pdf->MultiCell(0, 5, 'Thank you for your application for the position of ' . $position['designation'] . ' (' . $position['position_no'] . ') with the ' . $org['name'] . '.', 0, 'L');
        $pdf->Ln(2);

        $pdf->MultiCell(0, 5, 'We are pleased to inform you that you have been shortlisted for an interview. Please find your interview details below:', 0, 'L');
        $pdf->Ln(4);

        // Interview details in a box for better presentation
        $pdf->SetDrawColor(200, 200, 200); // Light gray border
        $pdf->SetFillColor(248, 248, 248); // Very light gray background

        // Calculate box height and position
        $boxHeight = 24;
        $boxY = $pdf->GetY();

        // Draw the box
        $pdf->Rect(20, $boxY, 170, $boxHeight, 'DF');

        // Add padding and content
        $pdf->SetY($boxY + 3);
        $pdf->SetX(25);

        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(25, 5, 'Date:', 0, 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 5, $interviewDate, 0, 1);

        $pdf->SetX(25);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(25, 5, 'Time:', 0, 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 5, $interviewTime, 0, 1);

        $pdf->SetX(25);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(25, 5, 'Venue:', 0, 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 5, $interviewSettings['interview_venue'] ?? 'To be confirmed', 0, 1);

        // Reset drawing color
        $pdf->SetDrawColor(0, 0, 0);

        $pdf->Ln(6);

        // Additional information
        $pdf->SetFont('helvetica', '', 10);
        $pdf->MultiCell(0, 5, 'Please ensure that you arrive at least 10 minutes before your scheduled interview time.', 0, 'L');
        $pdf->Ln(3);

        // Contact information
        $contactInfo = 'Should you have any questions or require further information, please do not hesitate to contact us';

        if (!empty($interviewSettings['interview_contacts'])) {
            $contactInfo .= ' on ' . $interviewSettings['interview_contacts'];
        }

        if (!empty($interviewSettings['reply_email'])) {
            $contactInfo .= ', or email: ' . $interviewSettings['reply_email'];
        }

        $contactInfo .= '.';

        $pdf->MultiCell(0, 5, $contactInfo, 0, 'L');
        $pdf->Ln(2);

        // Additional message if available
        if (!empty($interviewSettings['additional_messages'])) {
            $pdf->MultiCell(0, 5, $interviewSettings['additional_messages'], 0, 'L');
            $pdf->Ln(2);
        }

        $pdf->MultiCell(0, 5, 'We look forward to meeting you and wish you every success in the interview process.', 0, 'L');
        $pdf->Ln(4);

        // Sign off
        $pdf->Cell(0, 5, 'Yours sincerely,', 0, 1);
        $pdf->Ln(8);

        if (!empty($interviewSettings['sign_off_signature'])) {
            $pdf->MultiCell(0, 5, $interviewSettings['sign_off_signature'], 0, 'L');
        } else {
            $pdf->Cell(0, 5, 'HR Department', 0, 1);
            $pdf->Cell(0, 5, $org['name'], 0, 1);
        }

        $pdf->Ln(10);

        // Generate QR code if notification data is provided
        if ($notificationData) {
            try {
                // Create verification data - use the exact data that will be stored in interview_notices
                $verificationData = [
                    'applicant_id' => $applicant['id'],
                    'uid' => $notificationData['uid'],
                    'sender_email' => $notificationData['sender_email'],
                    'receiver_email' => $notificationData['receiver_email'],
                    'sent_datetime' => $notificationData['sent_datetime'],
                    'generated_by' => 'SelMasta'
                ];

                // Generate a verification code
                $verificationCode = substr(md5(json_encode($verificationData)), 0, 8);

                // Current Y position
                $currentY = $pdf->GetY();

                // Create user-friendly QR code data with ONLY the interview_notices field information
                $qrText = "INTERVIEW NOTIFICATION\n" .
                          "Notification ID: " . $notificationData['uid'] . "\n" .
                          "Sent: " . date('d/m/Y H:i', strtotime($notificationData['sent_datetime'])) . "\n" .
                          "From: " . $notificationData['sender_email'] . "\n" .
                          "To: " . $notificationData['receiver_email'];

                // QR code style - adjusted for larger content
                $style = [
                    'border' => false,
                    'vpadding' => 0,
                    'hpadding' => 0,
                    'fgcolor' => [0, 0, 0],
                    'bgcolor' => false,
                    'module_width' => 1,
                    'module_height' => 1,
                    'position' => 'R'
                ];

                // Calculate center position for QR code
                $qrX = 140;
                $qrWidth = 30;

                // Add QR code directly to PDF using TCPDF's built-in method
                // Reduced size for better appearance
                $pdf->write2DBarcode($qrText, 'QRCODE,M', $qrX, $currentY, $qrWidth, $qrWidth, $style);

                // Add verification text below QR code - precisely aligned with QR code center
                $pdf->SetFont('helvetica', 'I', 8);
                // Position text at the exact same X position and width as the QR code
                $pdf->SetXY($qrX, $currentY + $qrWidth + 2);
                $pdf->Cell($qrWidth, 5, 'Scan for verification', 0, 1, 'C');
            } catch (\Exception $e) {
                // Log the error but continue without the QR code
                log_message('error', 'QR code generation error: ' . $e->getMessage());

                // Add a note instead of the QR code
                $pdf->SetFont('helvetica', 'I', 9);
                $pdf->SetXY(130, $pdf->GetY());
                $pdf->Cell(60, 5, 'QR code unavailable', 0, 1, 'C');

                // Add a small explanation
                $pdf->SetFont('helvetica', 'I', 8);
                $pdf->SetXY(130, $pdf->GetY());
                $pdf->Cell(60, 5, 'Please refer to the printed details above', 0, 1, 'C');
            }
        }

        // Save PDF to a temporary file
        $tempFile = FCPATH . 'writable/temp/interview_notification_' . $applicant['id'] . '_' . time() . '.pdf';

        // Make sure the temp directory exists
        $tempDir = FCPATH . 'writable/temp';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0777, true);
        }

        $pdf->Output($tempFile, 'F');

        return $tempFile;
        } catch (\Exception $e) {
            log_message('error', 'PDF generation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification email with PDF attachment
     */
    private function sendNotificationEmail($applicant, $position, $org, $interviewSettings, $pdfPath, $recipientEmail)
    {
        $email = \Config\Services::email();

        // Set email parameters
        $email->setFrom('<EMAIL>', 'SelMasta - ' . $org['name']);

        // Use the provided recipient email
        $email->setTo($recipientEmail);

        // Set reply-to address if available
        if (!empty($interviewSettings['reply_email'])) {
            $email->setReplyTo($interviewSettings['reply_email']);
        }

        $email->setSubject('Interview Notification - ' . $position['designation'] . ' (' . $position['position_no'] . ')');

        // Get applicant's first name with error checking
        $nameParts = explode(' ', $applicant['name'] ?? 'Applicant');
        $firstName = $nameParts[0];

        // Email body
        $message = '<p>Dear Sir/Madam,</p>';
        $message .= '<p>Please find attached the interview notification letter for ' . ($applicant['name'] ?? 'the applicant') . ' for the position of ' . $position['designation'] . ' (' . $position['position_no'] . ').</p>';

        // Format interview date and time with error checking
        $interviewDate = isset($applicant['date']) ? date('l, j F Y', strtotime($applicant['date'])) : 'the scheduled date';
        $interviewTime = isset($applicant['start_time']) ? date('h:i A', strtotime($applicant['start_time'])) : 'the scheduled time';

        $message .= '<p>The interview is scheduled for ' . $interviewDate . ' at ' . $interviewTime . '.</p>';

        if (!empty($interviewSettings['interview_venue'])) {
            $message .= '<p>Venue: ' . $interviewSettings['interview_venue'] . '</p>';
        }

        if (!empty($interviewSettings['interview_contacts'])) {
            $message .= '<p>For any inquiries, please contact: ' . nl2br($interviewSettings['interview_contacts']) . '</p>';
        }

        $message .= '<p>Regards,<br>' . $interviewSettings['sign_off_signatures'] . '</p>';

        $email->setMessage($message);

        // Attach PDF
        $email->attach($pdfPath);

        // Send email
        if ($email->send()) {
            return [
                'status' => true,
                'message' => 'Email sent successfully'
            ];
        } else {
            return [
                'status' => false,
                'message' => 'Failed to send email',
                'error' => $email->printDebugger(['headers'])
            ];
        }
    }
}
